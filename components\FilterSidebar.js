export default function FilterSidebar({ filters, setFilters }) {
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-semibold mb-3">User Type</h3>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="userType"
              value="all"
              checked={filters.userType === "all"}
              onChange={(e) => handleFilterChange("userType", e.target.value)}
              className="w-4 h-4 text-[#DC143C] focus:ring-[#DC143C] focus:ring-2"
              style={{
                accentColor: '#DC143C'
              }}
            />
            <span>All</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="userType"
              value="seeker"
              checked={filters.userType === "seeker"}
              onChange={(e) => handleFilterChange("userType", e.target.value)}
              className="w-4 h-4 text-[#DC143C] focus:ring-[#DC143C] focus:ring-2"
              style={{
                accentColor: '#DC143C'
              }}
            />
            <span>Job Seekers</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="userType"
              value="referrer"
              checked={filters.userType === "referrer"}
              onChange={(e) => handleFilterChange("userType", e.target.value)}
              className="w-4 h-4 text-[#DC143C] focus:ring-[#DC143C] focus:ring-2"
              style={{
                accentColor: '#DC143C'
              }}
            />
            <span>Referrers</span>
          </label>
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-3">Payment Type</h3>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="paymentType"
              value="percentage"
              checked={filters.paymentType === "percentage"}
              onChange={(e) =>
                handleFilterChange("paymentType", e.target.value)
              }
              className="w-4 h-4 text-[#DC143C] focus:ring-[#DC143C] focus:ring-2"
              style={{
                accentColor: '#DC143C'
              }}
            />
            <span>Percentage</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="paymentType"
              value="fixed"
              checked={filters.paymentType === "fixed"}
              onChange={(e) =>
                handleFilterChange("paymentType", e.target.value)
              }
              className="w-4 h-4 text-[#DC143C] focus:ring-[#DC143C] focus:ring-2"
              style={{
                accentColor: '#DC143C'
              }}
            />
            <span>Fixed Amount</span>
          </label>
        </div>
      </div>

      {filters.paymentType === "percentage" && (
        <div>
          <h3 className="font-semibold mb-3">Percentage Range</h3>
          <div className="space-y-2">
            <input
              type="number"
              placeholder="Min %"
              value={filters.minPercentage}
              onChange={(e) =>
                handleFilterChange("minPercentage", e.target.value)
              }
              className="w-full px-3 py-2 border rounded"
              style={{ backgroundColor: '#F9FAFB' }}
              min="0"
              max="100"
            />
            <input
              type="number"
              placeholder="Max %"
              value={filters.maxPercentage}
              onChange={(e) =>
                handleFilterChange("maxPercentage", e.target.value)
              }
              className="w-full px-3 py-2 border rounded"
              style={{ backgroundColor: '#F9FAFB' }}
              min="0"
              max="100"
            />
          </div>
        </div>
      )}

      {filters.paymentType === "fixed" && (
        <div>
          <h3 className="font-semibold mb-3">Amount Range</h3>
          <div className="space-y-2">
            <input
              type="number"
              placeholder="Min Amount"
              value={filters.minFixed}
              onChange={(e) => handleFilterChange("minFixed", e.target.value)}
              className="w-full px-3 py-2 border rounded"
              style={{ backgroundColor: '#F9FAFB' }}
              min="0"
            />
            <input
              type="number"
              placeholder="Max Amount"
              value={filters.maxFixed}
              onChange={(e) => handleFilterChange("maxFixed", e.target.value)}
              className="w-full px-3 py-2 border rounded"
              style={{ backgroundColor: '#F9FAFB' }}
              min="0"
            />
          </div>
        </div>
      )}

      <div>
        <h3 className="font-semibold mb-3">Job Role</h3>
        <select
          value={filters.jobRole}
          onChange={(e) => handleFilterChange("jobRole", e.target.value)}
          className="w-full px-3 py-2 border rounded"
          style={{ backgroundColor: '#F9FAFB' }}
        >
          <option value="all">All Roles</option>
          <option value="software_developer">Software Developer</option>
          <option value="designer">Designer</option>
          <option value="product_manager">Product Manager</option>
          <option value="data_scientist">Data Scientist</option>
          <option value="marketing">Marketing</option>
          <option value="sales">Sales</option>
          <option value="other">Other</option>
        </select>
      </div>
    </div>
  );
}
