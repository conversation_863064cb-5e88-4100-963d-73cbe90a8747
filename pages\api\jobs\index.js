import { query } from "../../../lib/db";

export default async function handler(req, res) {
  if (req.method === "GET") {
    try {
      const { page = 1, limit = 12, search = "" } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Build search filter
      let searchFilter = "";
      if (search.trim()) {
        searchFilter = `AND (jp.title LIKE '%${search}%' OR jp.description LIKE '%${search}%' OR jp.job_role LIKE '%${search}%')`;
      }

      const jobs = await query(`
        SELECT
          jp.*,
          u.full_name as user_name,
          u.user_type,
          COUNT(DISTINCT a.id) as applications_count,
          COUNT(DISTINCT r.id) as total_reviews,
          AVG(r.rating) as average_rating
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        LEFT JOIN applications a ON jp.id = a.job_post_id
        LEFT JOIN reviews r ON u.id = r.reviewed_user_id
        WHERE jp.status = 'active'
          AND (jp.visibility = 'public' OR jp.visibility = 'anonymous')
          ${searchFilter}
        GROUP BY jp.id, u.id
        ORDER BY jp.created_at DESC
        LIMIT ? OFFSET ?
      `, [parseInt(limit), offset]);

      // Get total count for pagination
      const countResult = await query(`
        SELECT COUNT(DISTINCT jp.id) as total
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        WHERE jp.status = 'active'
          AND (jp.visibility = 'public' OR jp.visibility = 'anonymous')
          ${searchFilter}
      `);

      const totalJobs = countResult[0].total;
      const totalPages = Math.ceil(totalJobs / parseInt(limit));

      res.status(200).json({
        jobs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalJobs,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      });
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
      res.status(500).json({ error: "Failed to fetch jobs" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}
